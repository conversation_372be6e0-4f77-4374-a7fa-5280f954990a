from ..db import get_session
import json
import os
from dotenv import load_dotenv
from sqlmodel import Session, select
from ..db import engine
from ..models import *
from ..models import BehaviorType  # Explicitly import BehaviorType
from datetime import datetime, timezone
import random
from sqlalchemy import and_
from ..utils.score_calculator import (
    calculate_competency_scores,
    calculate_factor_scores_for_evaluation,
    calculate_category_scores_for_evaluation
)

# Load environment variables
load_dotenv()

def seed_roles():
    roles = ["<PERSON><PERSON><PERSON>", "Consultor", "Manager", "Director", "<PERSON>cio"]
    with get_session() as session:
        for role_name in roles:
            if not session.query(Role).filter_by(name=role_name).first():
                session.add(Role(name=role_name))
        session.commit()
    print("Roles seeded.")

def seed_performance_competencies():
    with open("assets/competencias_performance.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                # Map the JSON category to the CompetencyCategory enum
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                # Convert the category to lowercase and remove any spaces
                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Technical competencies seeded.")

def seed_potential_competencies():
    with open("assets/competencias_potencial.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Potential competencies seeded.")

def seed_competency_role_map_from_json():
    with open("assets/role_comp_map.json", "r", encoding="utf-8") as f:
        role_comp_map = json.load(f)

    with get_session() as session:
        for entry in role_comp_map:
            role = session.query(Role).filter_by(name=entry["role"]).first()
            if not role:
                print(f"[WARNING] Role not found: {entry['role']}")
                continue

            for comp_code in entry["competencies"]:
                competency = session.query(Competency).filter_by(code=str(comp_code)).first()
                if not competency:
                    print(f"[WARNING] Competency code not found: {comp_code}")
                    continue

                exists = session.query(CompetencyRoleMap).filter_by(
                    role_id=role.id,
                    competency_id=competency.id
                ).first()

                if not exists:
                    session.add(CompetencyRoleMap(
                        role_id=role.id,
                        competency_id=competency.id,
                        weight=1.0
                    ))

        session.commit()
    print("CompetencyRoleMap seeded from JSON.")

def seed_users_and_projects():
    # Get user Azure ID from environment variable or use a default
    user_azure_id = os.getenv("USER_AZURE_ID")

    users_data = [
        {"name": "Bruno Bolla Pons", "email": "<EMAIL>", "azure_id": user_azure_id, "role_name": "Analista", "main_evaluator": True},
        {"name": "Ana García López", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000001", "role_name": "Analista", "main_evaluator": False},
        {"name": "Carlos Ruiz Martínez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000002", "role_name": "Consultor", "main_evaluator": True},
        {"name": "Laura Torres Jiménez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000005", "role_name": "Analista", "main_evaluator": False},
        {"name": "Javier Moreno Rodríguez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000006", "role_name": "Consultor", "main_evaluator": True},
    ]

    projects_data = [
        {"code": "PRJ001", "name": "AI Strategy", "start_date": datetime(2024, 1, 1), "status": ProjectStatus.ACTIVE},
        {"code": "PRJ002", "name": "Digital Transformation", "start_date": datetime(2024, 2, 15), "status": ProjectStatus.ACTIVE},
    ]

    user_project_links = [
        #p1
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),

        # Project 2 (Digital Transformation)
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
    ]

    with get_session() as session:
        # First ensure we have roles
        roles = session.query(Role).all()
        if not roles:
            print("Warning: No roles found. Please run seed_roles() first.")
            return

        # Create users with roles
        for user_data in users_data:
            if not session.query(User).filter_by(email=user_data["email"]).first():
                role = session.query(Role).filter_by(name=user_data["role_name"]).first()
                if not role:
                    print(f"Warning: Role '{user_data['role_name']}' not found for user {user_data['email']}")
                    continue

                role_name = user_data.pop("role_name")
                user_data["role_id"] = role.id

                session.add(User(**user_data))
        session.commit()

        # Create projects
        for proj in projects_data:
            if not session.query(Project).filter_by(code=proj["code"]).first():
                session.add(Project(**proj))
        session.commit()

        # Create user-project links
        for email, code in user_project_links:
            user = session.query(User).filter_by(email=email).first()
            project = session.query(Project).filter_by(code=code).first()
            if user and project:
                if not session.query(UserProject).filter_by(user_id=user.id, project_id=project.id).first():
                    session.add(UserProject(user_id=user.id, project_id=project.id))
        session.commit()

def seed_evaluations():
    with get_session() as session:
        # Fetch all required data upfront
        users = session.query(User).all()
        projects = session.query(Project).all()
        roles = session.query(Role).all()

        batch_size = 100  # Process evaluations in batches
        current_batch = []

        # Get user 1 specifically
        user_1 = session.query(User).filter_by(id=1).first()
        if user_1:
            print(f"Creating diverse evaluations for user: {user_1.name} (ID: {user_1.id})")

            # Get competencies mapped to user_1's role
            user_1_competencies = (
                session.query(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Get questions only for user_1's role competencies
            questions = (
                session.query(EvaluationQuestion)
                .join(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Group questions by competency_id and behavior_type for faster access
            questions_by_competency = {}
            for q in questions:
                if q.competency_id not in questions_by_competency:
                    questions_by_competency[q.competency_id] = []
                questions_by_competency[q.competency_id].append(q)

            # Create evaluations for user 1 for each project
            for project in projects:
                print(f"Creating evaluation for project: {project.name} (ID: {project.id})")

                # Create self-evaluation for user 1 for this project with varied responses
                self_eval = Evaluation(
                    evaluation_type=EvaluationType.PERFORMANCE,
                    evaluator_type=EvaluatorType.SELF,
                    evaluator_id=user_1.id,
                    evaluatee_id=user_1.id,
                    project_id=project.id,
                    status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                )
                session.add(self_eval)
                session.flush()  # Get the ID

                # Create answers with varied responses based on behavior type
                answers = []
                for question in questions:
                    # Determine response based on behavior type
                    if question.behavior_type == BehaviorType.EXPERT:
                        # Expert behavior - 70% positive
                        rand = random.random()
                        if rand < 0.7:
                            response = ResponseType.SI
                        elif rand < 0.85:  # 15% sometimes
                            response = ResponseType.A_VECES
                        else:  # 15% no
                            response = ResponseType.NO
                    elif question.behavior_type == BehaviorType.TALENTED:
                        # Talented behavior - 50% positive
                        rand = random.random()
                        if rand < 0.5:
                            response = ResponseType.SI
                        elif rand < 0.7:  # 20% sometimes
                            response = ResponseType.A_VECES
                        else:  # 30% no
                            response = ResponseType.NO
                    else:
                        # Low skill behavior - 30% positive
                        rand = random.random()
                        if rand < 0.3:
                            response = ResponseType.SI
                        elif rand < 0.6:  # 30% sometimes
                            response = ResponseType.A_VECES
                        else:  # 40% no
                            response = ResponseType.NO

                    answer = EvaluationAnswer(
                        evaluation_id=self_eval.id,
                        question_id=question.id,
                        response=response,
                        comment=f"Self-evaluation for {user_1.name} on project {project.name}: {question.text[:50]}..."
                    )
                    answers.append(answer)

                session.bulk_save_objects(answers)
                current_batch.append(self_eval.id)

                # Create peer evaluations from other users to user 1 with varied patterns
                project_users = [
                    up.user for up in session.query(UserProject)
                    .filter_by(project_id=project.id)
                    .all() if up.user.id != user_1.id
                ]

                # Create peer evaluations with different patterns for each peer
                for idx, peer in enumerate(project_users[:3]):  # Increase to 3 peers per project
                    peer_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.PEER,
                        evaluator_id=peer.id,
                        evaluatee_id=user_1.id,
                        project_id=project.id,
                        status=EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(peer_eval)
                    session.flush()

                    # Create answers with different patterns for each peer
                    peer_answers = []
                    for question in questions:
                        # Adjust response probability based on peer's role
                        peer_role = session.query(Role).filter_by(id=peer.role_id).first()
                        if peer_role.name in ["Analista", "Consultor"]:
                            # Junior roles - better at technical, worse at leadership
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_TECNICAS:
                                # 85% positive
                                rand = random.random()
                                if rand < 0.85:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 10% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 50% positive
                                rand = random.random()
                                if rand < 0.5:
                                    response = ResponseType.SI
                                elif rand < 0.7:  # 20% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 30% no
                                    response = ResponseType.NO
                        else:
                            # Senior roles - better at leadership, mixed at technical
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES:
                                # 90% positive
                                rand = random.random()
                                if rand < 0.9:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 5% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 70% positive
                                rand = random.random()
                                if rand < 0.7:
                                    response = ResponseType.SI
                                elif rand < 0.85:  # 15% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 15% no
                                    response = ResponseType.NO

                        answer = EvaluationAnswer(
                            evaluation_id=peer_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=f"Peer evaluation from {peer.name} for {user_1.name} on {question.text[:50]}..."
                        )
                        peer_answers.append(answer)

                    session.bulk_save_objects(peer_answers)
                    current_batch.append(peer_eval.id)

                # Process batch if it reaches the batch size
                if len(current_batch) >= batch_size:
                    session.commit()
                    process_evaluation_batch(current_batch, session)
                    current_batch = []

        # Generate random evaluations for other users
        for project in projects:
            project_users = [
                up.user for up in session.query(UserProject)
                .filter_by(project_id=project.id)
                .all() if up.user.id != 1  # Skip user 1 as we've already created custom evals
            ]

            for user in project_users:
                # Get competencies mapped to user's role
                user_competencies = (
                    session.query(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Get questions only for user's role competencies
                user_questions = (
                    session.query(EvaluationQuestion)
                    .join(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Self evaluation (50% chance)
                if random.random() < 0.50:
                    self_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.SELF,
                        evaluator_id=user.id,
                        evaluatee_id=user.id,
                        project_id=project.id,
                        status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(self_eval)
                    session.flush()  # Get the ID

                    # Create all answers for this evaluation at once
                    answers = []
                    for question in user_questions:
                        # 80% positive, 10% sometimes, 10% no
                        rand = random.random()
                        if rand < 0.8:
                            response = ResponseType.SI
                        elif rand < 0.9:
                            response = ResponseType.A_VECES
                        else:
                            response = ResponseType.NO

                        answer = EvaluationAnswer(
                            evaluation_id=self_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=f"Self-evaluation comment for {user.name} on {question.text}"
                        )
                        answers.append(answer)

                    session.bulk_save_objects(answers)
                    current_batch.append(self_eval.id)

                    # Process batch if it reaches the batch size
                    if len(current_batch) >= batch_size:
                        session.commit()
                        process_evaluation_batch(current_batch, session)
                        current_batch = []

        # Process any remaining evaluations
        if current_batch:
            session.commit()
            process_evaluation_batch(current_batch, session)

    print("Evaluations and scores seeded with diverse patterns.")

def process_evaluation_batch(eval_ids: List[int], session: Session):
    """Process a batch of evaluations to calculate their scores."""
    for eval_id in eval_ids:
        try:
            # Calculate all scores in one transaction
            comp_scores = calculate_competency_scores(eval_id, session)
            session.bulk_save_objects(comp_scores)

            factor_scores = calculate_factor_scores_for_evaluation(eval_id, session)
            if factor_scores:
                session.bulk_save_objects(factor_scores)

            category_scores = calculate_category_scores_for_evaluation(eval_id, session)
            if category_scores:
                session.bulk_save_objects(category_scores)

            session.commit()
            print(f"✓ Processed scores for evaluation {eval_id}")

        except Exception as e:
            print(f"✗ Error processing scores for evaluation {eval_id}: {str(e)}")
            session.rollback()
            continue

def seed_action_plans():
    """Seed comprehensive action plans for user ID 1 (Bruno Bolla Pons)."""
    with get_session() as session:
        # Get user 1 (Bruno Bolla Pons)
        user_1 = session.get(User, 1)
        if not user_1:
            print("User 1 not found. Please run seed_users_and_projects() first.")
            return

        # Get evaluations for user 1 where he is the evaluatee
        evaluations = session.exec(
            select(Evaluation).where(Evaluation.evaluatee_id == 1)
        ).all()

        if not evaluations:
            print("No evaluations found for user 1. Please run seed_evaluations() first.")
            return

        # Use the first evaluation for action plans
        evaluation = evaluations[0]
        print(f"Creating action plans for evaluation ID: {evaluation.id}")

        # Comprehensive action plans data organized by category and factor
        action_plans_data = {
            # Competencias Comportamentales
            "Competencias Comportamentales": {
                "Desarrollo personal": {
                    "category": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "text": """**Plan de Desarrollo Personal - Q1 2024**

**Objetivos:**
• Mejorar auto-consciencia mediante feedback 360° mensual
• Desarrollar adaptabilidad en proyectos con cambios frecuentes
• Fortalecer aprendizaje continuo con certificaciones técnicas

**Acciones (12 semanas):**
1. **Auto-consciencia (1-4):** Feedback de 3 colegas, diario reflexión semanal, coaching mensual
2. **Adaptabilidad (5-8):** Liderar 2 iniciativas de cambio, documentar lecciones aprendidas
3. **Aprendizaje (9-12):** Certificación Scrum Master, 2 conferencias sector, presentación equipo

**Métricas:**
- +20% puntuación feedback 360°
- 100% certificaciones completadas
- 2 proyectos cambio liderados exitosamente

**Recursos:** €1,500 certificaciones, 4h/semana, mentor asignado"""
                }
            },

            # Competencias Técnicas
            "Competencias Tecnicas": {
                "Bloque 0: Preparar el Engagement": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """**Plan de Mejora - Preparación de Engagement**

**Enfoque:** Conocimientos Management y Comprensión Cliente

**Diagnóstico:** Fortalezas técnicas sólidas, oportunidad en frameworks management y conocimiento sectorial

**Plan 8 semanas:**
**Fase 1 (1-3):** Frameworks McKinsey 7S, BCG Matrix, Porter's Five Forces. Curso Strategic Management (Coursera). 3 casos industria cliente
**Fase 2 (4-6):** Investigación sector financiero. 5 entrevistas stakeholders. Mapa ecosistema competitivo
**Fase 3 (7-8):** Propuesta valor mejorada. Presentación insights. Feedback senior manager

**Entregables:** Análisis sectorial (15 pág), Framework engagement, Presentación mejores prácticas
**Seguimiento:** Revisión semanal supervisor, evaluación cliente próximo proyecto"""
                },

                "Bloque 1: Enmarcar el Problema": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """**Plan Desarrollo - Estructuración Problemas**

**Objetivo:** Mejorar definición preguntas clave y estructurar hipótesis

**8 semanas:**
**1-2:** Workshop Issue Tree Methodology. 5 casos reales. Mentoring consultor senior
**3-4:** Framework MECE. 3 hipótesis problemas negocio. Validación equipo
**5-6:** Técnica "5 Whys" root causes. Banco preguntas industrias. Práctica stakeholders
**7-8:** Liderar sesión problem framing. Documentar lecciones. Presentar metodología

**Herramientas:** Logic trees, hypothesis-driven approach, stakeholder mapping, root cause analysis
**Meta:** -30% tiempo definición problemas proyectos futuros"""
                },

                "Bloque 2: Diseñar el Analisis": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """**Plan Fortalecimiento - Diseño Analítico**

**Competencias:** 1) Propósito análisis 2) Diseño técnico robusto 3) Validación metodológica

**6 semanas:**
**Módulo 1 (1-2):** Curso Analytics Strategy (edX). 10 casos análisis exitosos. Template propósito analítico
**Módulo 2 (3-4):** Certificación Python/R/Tableau. Workshop statistical significance. Colaboración data scientist senior
**Módulo 3 (5-6):** Checklist validación diseño. Peer review 2 consultores. Presentación comité técnico

**Proyectos:** Rediseñar análisis anterior, liderar diseño próximo engagement, guía mejores prácticas
**Métricas:** 95% análisis pasan validación primera vez, -25% tiempo iteraciones, feedback positivo cliente"""
                }
            },

            # Aprendizaje
            "Aprendizaje": {
                "Desarrollo personal": {
                    "category": CompetencyCategory.APRENDIZAJE,
                    "text": """**Plan Aprendizaje Continuo y Crecimiento Personal**

**Visión:** Profesional que aprende proactivamente y se adapta rápidamente a nuevos desafíos

**12 semanas - 3 Pilares:**

**Pilar 1: Aprendizaje al Vuelo (1-4)**
• Metodología "Learning in Flow of Work". 30min diarios micro-learning. Red 5 profesionales industrias. Insights diarios knowledge system

**Pilar 2: Auto-consciencia (5-8)**
• Evaluación 360° trimestral. Coaching mensual mentor externo. Journaling semanal decisiones. StrengthsFinder

**Pilar 3: Autodesarrollo (9-12)**
• Plan carrera 3 años milestones. Certificaciones técnicas. Proyectos stretch. Marca personal thought leadership

**Actividades:** Data Science (Coursera), Design Thinking (IDEO), Storytelling consultores, liderazgo jóvenes, presentación avanzada, negociación

**Networking:** Speaker 2 eventos, 4 artículos LinkedIn, mentorear 2 juniors

**Métricas:** 100% certificaciones, +25% engagement contenido, feedback positivo, promoción 12 meses
**Recursos:** €2,000, 6h/semana, mentor senior, plataformas learning"""
                }
            }
        }

        # Create action plans for each category/factor
        created_count = 0
        for category_name, factors in action_plans_data.items():
            for factor_name, plan_data in factors.items():
                # Check if action plan already exists
                existing_plan = session.exec(
                    select(ActionPlan).where(
                        ActionPlan.evaluation_id == evaluation.id,
                        ActionPlan.factor == factor_name,
                        ActionPlan.category == plan_data["category"]
                    )
                ).first()

                if not existing_plan:
                    action_plan = ActionPlan(
                        evaluation_id=evaluation.id,
                        category=plan_data["category"],
                        factor=factor_name,
                        action_plan_text=plan_data["text"],
                        evaluator_id=3,  # Carlos Ruiz Martínez (main evaluator)
                        created_at=datetime.now(timezone.utc)
                    )
                    session.add(action_plan)
                    created_count += 1
                    print(f"Created action plan for {category_name} - {factor_name}")

        session.commit()
        print(f"Successfully created {created_count} action plans for user {user_1.name}")

if __name__ == "__main__":
    seed_roles()
    seed_performance_competencies()
    seed_potential_competencies()
    seed_competency_role_map_from_json()
    seed_users_and_projects()
    seed_evaluations()
    seed_action_plans()
